import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:possystem/features/ai_menu/models/ai_menu_models.dart';
import 'package:possystem/helpers/logger.dart';

/// AI餐单识别服务
class AiMenuService {
  static final AiMenuService _instance = AiMenuService._internal();
  factory AiMenuService() => _instance;
  AiMenuService._internal();

  static const String _baseUrl = 'https://api.example.com/ai-menu'; // TODO: 替换为实际API地址
  static const Duration _timeout = Duration(seconds: 30);

  final http.Client _client = http.Client();

  /// 上传图片并开始识别
  Future<AiMenuRecognitionResult> uploadImage(File imageFile) async {
    try {
      Log.out('Starting AI menu recognition for image: ${imageFile.path}', 'ai_menu_service');

      // 读取图片并转换为base64
      final imageBytes = await imageFile.readAsBytes();
      final imageBase64 = base64Encode(imageBytes);

      final request = AiMenuUploadRequest(
        imageBase64: imageBase64,
        imageFormat: _getImageFormat(imageFile.path),
        metadata: {
          'timestamp': DateTime.now().toIso8601String(),
          'fileSize': imageBytes.length,
        },
      );

      final response = await _client
          .post(
            Uri.parse('$_baseUrl/upload'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode(request.toJson()),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final result = AiMenuRecognitionResult.fromJson(
          jsonDecode(response.body) as Map<String, dynamic>,
        );
        Log.out('AI menu recognition started successfully: ${result.sessionId}', 'ai_menu_service');
        return result;
      } else {
        throw AiMenuException(
          'Upload failed with status: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      Log.err(e, 'ai_menu_service_upload');
      rethrow;
    }
  }

  /// 获取分类识别结果
  Future<List<AiMenuCategory>> getCategoryRecognition(String sessionId) async {
    try {
      Log.out('Getting category recognition for session: $sessionId', 'ai_menu_service');

      final response = await _client
          .get(
            Uri.parse('$_baseUrl/categories/$sessionId'),
            headers: {
              'Accept': 'application/json',
            },
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final categories = (data['categories'] as List)
            .map((item) => AiMenuCategory.fromJson(item as Map<String, dynamic>))
            .toList();
        
        Log.out('Retrieved ${categories.length} categories', 'ai_menu_service');
        return categories;
      } else {
        throw AiMenuException(
          'Get categories failed with status: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      Log.err(e, 'ai_menu_service_categories');
      rethrow;
    }
  }

  /// 确认分类并开始菜品识别
  Future<List<AiMenuItem>> confirmCategoriesAndGetItems(
    String sessionId,
    List<AiMenuCategory> confirmedCategories,
  ) async {
    try {
      Log.out('Confirming categories and getting items for session: $sessionId', 'ai_menu_service');

      final response = await _client
          .post(
            Uri.parse('$_baseUrl/confirm-categories/$sessionId'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode({
              'categories': confirmedCategories.map((c) => c.toJson()).toList(),
            }),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final items = (data['items'] as List)
            .map((item) => AiMenuItem.fromJson(item as Map<String, dynamic>))
            .toList();
        
        Log.out('Retrieved ${items.length} menu items', 'ai_menu_service');
        return items;
      } else {
        throw AiMenuException(
          'Confirm categories failed with status: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      Log.err(e, 'ai_menu_service_confirm_categories');
      rethrow;
    }
  }

  /// 提交最终确认的数据
  Future<AiMenuSubmitResponse> submitFinalData(AiMenuSubmitRequest request) async {
    try {
      Log.out('Submitting final data for session: ${request.sessionId}', 'ai_menu_service');

      final response = await _client
          .post(
            Uri.parse('$_baseUrl/submit'),
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: jsonEncode(request.toJson()),
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final result = AiMenuSubmitResponse.fromJson(
          jsonDecode(response.body) as Map<String, dynamic>,
        );
        Log.out('Final data submitted successfully', 'ai_menu_service');
        return result;
      } else {
        throw AiMenuException(
          'Submit failed with status: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      Log.err(e, 'ai_menu_service_submit');
      rethrow;
    }
  }

  /// 获取识别状态
  Future<AiMenuStatus> getRecognitionStatus(String sessionId) async {
    try {
      final response = await _client
          .get(
            Uri.parse('$_baseUrl/status/$sessionId'),
            headers: {
              'Accept': 'application/json',
            },
          )
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final statusString = data['status'] as String;
        return AiMenuStatus.values.firstWhere(
          (status) => status.toString().split('.').last == statusString,
          orElse: () => AiMenuStatus.error,
        );
      } else {
        throw AiMenuException(
          'Get status failed with status: ${response.statusCode}',
          response.body,
        );
      }
    } catch (e) {
      Log.err(e, 'ai_menu_service_status');
      return AiMenuStatus.error;
    }
  }

  /// 获取图片格式
  String _getImageFormat(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'jpeg';
      case 'png':
        return 'png';
      case 'webp':
        return 'webp';
      default:
        return 'jpeg';
    }
  }

  /// 释放资源
  void dispose() {
    _client.close();
  }
}

/// AI餐单识别异常
class AiMenuException implements Exception {
  final String message;
  final String? details;

  const AiMenuException(this.message, [this.details]);

  @override
  String toString() {
    return 'AiMenuException: $message${details != null ? '\nDetails: $details' : ''}';
  }
}
