import 'package:json_annotation/json_annotation.dart';

part 'ai_menu_models.g.dart';

/// AI餐单识别 - 上传图片请求
@JsonSerializable()
class AiMenuUploadRequest {
  final String imageBase64;
  final String? imageFormat;
  final Map<String, dynamic>? metadata;

  const AiMenuUploadRequest({
    required this.imageBase64,
    this.imageFormat = 'jpeg',
    this.metadata,
  });

  factory AiMenuUploadRequest.fromJson(Map<String, dynamic> json) =>
      _$AiMenuUploadRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AiMenuUploadRequestToJson(this);
}

/// AI餐单识别 - 分类识别结果
@JsonSerializable()
class AiMenuCategory {
  final String id;
  final String name;
  final String? description;
  final double confidence;
  final int sortOrder;
  final bool isConfirmed;

  const AiMenuCategory({
    required this.id,
    required this.name,
    this.description,
    required this.confidence,
    this.sortOrder = 0,
    this.isConfirmed = false,
  });

  factory AiMenuCategory.fromJson(Map<String, dynamic> json) =>
      _$AiMenuCategoryFromJson(json);

  Map<String, dynamic> toJson() => _$AiMenuCategoryToJson(this);

  AiMenuCategory copyWith({
    String? id,
    String? name,
    String? description,
    double? confidence,
    int? sortOrder,
    bool? isConfirmed,
  }) {
    return AiMenuCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      confidence: confidence ?? this.confidence,
      sortOrder: sortOrder ?? this.sortOrder,
      isConfirmed: isConfirmed ?? this.isConfirmed,
    );
  }
}

/// AI餐单识别 - 菜品识别结果
@JsonSerializable()
class AiMenuItem {
  final String id;
  final String categoryId;
  final String name;
  final String? description;
  final double price;
  final int quantity;
  final double confidence;
  final bool isConfirmed;
  final Map<String, dynamic>? metadata;

  const AiMenuItem({
    required this.id,
    required this.categoryId,
    required this.name,
    this.description,
    required this.price,
    this.quantity = 1,
    required this.confidence,
    this.isConfirmed = false,
    this.metadata,
  });

  factory AiMenuItem.fromJson(Map<String, dynamic> json) =>
      _$AiMenuItemFromJson(json);

  Map<String, dynamic> toJson() => _$AiMenuItemToJson(this);

  AiMenuItem copyWith({
    String? id,
    String? categoryId,
    String? name,
    String? description,
    double? price,
    int? quantity,
    double? confidence,
    bool? isConfirmed,
    Map<String, dynamic>? metadata,
  }) {
    return AiMenuItem(
      id: id ?? this.id,
      categoryId: categoryId ?? this.categoryId,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      confidence: confidence ?? this.confidence,
      isConfirmed: isConfirmed ?? this.isConfirmed,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// AI餐单识别 - 完整识别结果
@JsonSerializable()
class AiMenuRecognitionResult {
  final String sessionId;
  final String imageUrl;
  final List<AiMenuCategory> categories;
  final List<AiMenuItem> items;
  final DateTime createdAt;
  final AiMenuStatus status;
  final String? errorMessage;

  const AiMenuRecognitionResult({
    required this.sessionId,
    required this.imageUrl,
    required this.categories,
    required this.items,
    required this.createdAt,
    required this.status,
    this.errorMessage,
  });

  factory AiMenuRecognitionResult.fromJson(Map<String, dynamic> json) =>
      _$AiMenuRecognitionResultFromJson(json);

  Map<String, dynamic> toJson() => _$AiMenuRecognitionResultToJson(this);

  AiMenuRecognitionResult copyWith({
    String? sessionId,
    String? imageUrl,
    List<AiMenuCategory>? categories,
    List<AiMenuItem>? items,
    DateTime? createdAt,
    AiMenuStatus? status,
    String? errorMessage,
  }) {
    return AiMenuRecognitionResult(
      sessionId: sessionId ?? this.sessionId,
      imageUrl: imageUrl ?? this.imageUrl,
      categories: categories ?? this.categories,
      items: items ?? this.items,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// AI餐单识别状态
enum AiMenuStatus {
  @JsonValue('uploading')
  uploading,
  @JsonValue('processing_categories')
  processingCategories,
  @JsonValue('confirming_categories')
  confirmingCategories,
  @JsonValue('processing_items')
  processingItems,
  @JsonValue('confirming_items')
  confirmingItems,
  @JsonValue('completed')
  completed,
  @JsonValue('error')
  error,
}

/// AI餐单识别 - 提交确认请求
@JsonSerializable()
class AiMenuSubmitRequest {
  final String sessionId;
  final List<AiMenuCategory> confirmedCategories;
  final List<AiMenuItem> confirmedItems;
  final Map<String, dynamic>? metadata;

  const AiMenuSubmitRequest({
    required this.sessionId,
    required this.confirmedCategories,
    required this.confirmedItems,
    this.metadata,
  });

  factory AiMenuSubmitRequest.fromJson(Map<String, dynamic> json) =>
      _$AiMenuSubmitRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AiMenuSubmitRequestToJson(this);
}

/// AI餐单识别 - 提交响应
@JsonSerializable()
class AiMenuSubmitResponse {
  final bool success;
  final String? message;
  final Map<String, dynamic>? data;

  const AiMenuSubmitResponse({
    required this.success,
    this.message,
    this.data,
  });

  factory AiMenuSubmitResponse.fromJson(Map<String, dynamic> json) =>
      _$AiMenuSubmitResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AiMenuSubmitResponseToJson(this);
}
