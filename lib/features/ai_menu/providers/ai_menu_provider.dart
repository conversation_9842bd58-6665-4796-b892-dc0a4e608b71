import 'dart:io';
import 'package:flutter/material.dart';
import 'package:possystem/features/ai_menu/models/ai_menu_models.dart';
import 'package:possystem/features/ai_menu/services/ai_menu_service.dart';
import 'package:possystem/helpers/logger.dart';

/// AI餐单识别状态管理
class AiMenuProvider extends ChangeNotifier {
  final AiMenuService _aiMenuService = AiMenuService();

  // 当前状态
  AiMenuStatus _status = AiMenuStatus.uploading;
  AiMenuStatus get status => _status;

  // 会话ID
  String? _sessionId;
  String? get sessionId => _sessionId;

  // 上传的图片
  File? _uploadedImage;
  File? get uploadedImage => _uploadedImage;

  // 识别结果
  List<AiMenuCategory> _categories = [];
  List<AiMenuCategory> get categories => _categories;

  List<AiMenuItem> _items = [];
  List<AiMenuItem> get items => _items;

  // 加载状态
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  // 错误信息
  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  // 进度信息
  String _progressMessage = '';
  String get progressMessage => _progressMessage;

  /// 重置状态
  void reset() {
    _status = AiMenuStatus.uploading;
    _sessionId = null;
    _uploadedImage = null;
    _categories.clear();
    _items.clear();
    _isLoading = false;
    _errorMessage = null;
    _progressMessage = '';
    notifyListeners();
  }

  /// 上传图片并开始识别
  Future<bool> uploadImage(File imageFile) async {
    try {
      _setLoading(true, 'Uploading image...');
      _uploadedImage = imageFile;
      _status = AiMenuStatus.uploading;
      _errorMessage = null;
      notifyListeners();

      final result = await _aiMenuService.uploadImage(imageFile);
      _sessionId = result.sessionId;
      _status = result.status;

      if (result.status == AiMenuStatus.processingCategories) {
        _setLoading(true, 'Processing categories...');
        await _pollForCategories();
      } else if (result.categories.isNotEmpty) {
        _categories = result.categories;
        _status = AiMenuStatus.confirmingCategories;
      }

      _setLoading(false);
      return true;
    } catch (e) {
      _handleError('Failed to upload image', e);
      return false;
    }
  }

  /// 轮询获取分类识别结果
  Future<void> _pollForCategories() async {
    if (_sessionId == null) return;

    try {
      int attempts = 0;
      const maxAttempts = 30; // 最多等待30秒
      const pollInterval = Duration(seconds: 1);

      while (attempts < maxAttempts) {
        final status = await _aiMenuService.getRecognitionStatus(_sessionId!);
        
        if (status == AiMenuStatus.confirmingCategories) {
          final categories = await _aiMenuService.getCategoryRecognition(_sessionId!);
          _categories = categories;
          _status = AiMenuStatus.confirmingCategories;
          break;
        } else if (status == AiMenuStatus.error) {
          throw Exception('Recognition failed on server');
        }

        attempts++;
        await Future.delayed(pollInterval);
        _setLoading(true, 'Processing categories... (${attempts}s)');
      }

      if (attempts >= maxAttempts) {
        throw Exception('Recognition timeout');
      }
    } catch (e) {
      _handleError('Failed to get categories', e);
    }
  }

  /// 更新分类
  void updateCategory(int index, AiMenuCategory updatedCategory) {
    if (index >= 0 && index < _categories.length) {
      _categories[index] = updatedCategory;
      notifyListeners();
    }
  }

  /// 添加分类
  void addCategory(AiMenuCategory category) {
    _categories.add(category);
    notifyListeners();
  }

  /// 删除分类
  void removeCategory(int index) {
    if (index >= 0 && index < _categories.length) {
      final removedCategoryId = _categories[index].id;
      _categories.removeAt(index);
      
      // 同时删除该分类下的所有菜品
      _items.removeWhere((item) => item.categoryId == removedCategoryId);
      notifyListeners();
    }
  }

  /// 确认分类并获取菜品
  Future<bool> confirmCategoriesAndGetItems() async {
    if (_sessionId == null) return false;

    try {
      _setLoading(true, 'Processing menu items...');
      _status = AiMenuStatus.processingItems;
      notifyListeners();

      final confirmedCategories = _categories.map((c) => c.copyWith(isConfirmed: true)).toList();
      final items = await _aiMenuService.confirmCategoriesAndGetItems(_sessionId!, confirmedCategories);
      
      _items = items;
      _status = AiMenuStatus.confirmingItems;
      _setLoading(false);
      return true;
    } catch (e) {
      _handleError('Failed to get menu items', e);
      return false;
    }
  }

  /// 更新菜品
  void updateItem(int index, AiMenuItem updatedItem) {
    if (index >= 0 && index < _items.length) {
      _items[index] = updatedItem;
      notifyListeners();
    }
  }

  /// 添加菜品
  void addItem(AiMenuItem item) {
    _items.add(item);
    notifyListeners();
  }

  /// 删除菜品
  void removeItem(int index) {
    if (index >= 0 && index < _items.length) {
      _items.removeAt(index);
      notifyListeners();
    }
  }

  /// 根据分类ID获取菜品
  List<AiMenuItem> getItemsByCategory(String categoryId) {
    return _items.where((item) => item.categoryId == categoryId).toList();
  }

  /// 提交最终数据
  Future<bool> submitFinalData() async {
    if (_sessionId == null) return false;

    try {
      _setLoading(true, 'Submitting data...');
      
      final confirmedCategories = _categories.map((c) => c.copyWith(isConfirmed: true)).toList();
      final confirmedItems = _items.map((i) => i.copyWith(isConfirmed: true)).toList();

      final request = AiMenuSubmitRequest(
        sessionId: _sessionId!,
        confirmedCategories: confirmedCategories,
        confirmedItems: confirmedItems,
        metadata: {
          'timestamp': DateTime.now().toIso8601String(),
          'totalCategories': confirmedCategories.length,
          'totalItems': confirmedItems.length,
        },
      );

      final response = await _aiMenuService.submitFinalData(request);
      
      if (response.success) {
        _status = AiMenuStatus.completed;
        _setLoading(false);
        return true;
      } else {
        throw Exception(response.message ?? 'Submit failed');
      }
    } catch (e) {
      _handleError('Failed to submit data', e);
      return false;
    }
  }

  /// 设置加载状态
  void _setLoading(bool loading, [String? message]) {
    _isLoading = loading;
    _progressMessage = message ?? '';
    notifyListeners();
  }

  /// 处理错误
  void _handleError(String message, dynamic error) {
    Log.err(error, 'ai_menu_provider');
    _errorMessage = '$message: ${error.toString()}';
    _status = AiMenuStatus.error;
    _setLoading(false);
  }

  /// 清除错误
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _aiMenuService.dispose();
    super.dispose();
  }
}
