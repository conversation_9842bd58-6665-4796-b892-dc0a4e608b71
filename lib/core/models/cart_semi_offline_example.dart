import 'cart_models.dart';
import '../services/cart_semi_offline_service.dart';

/// 半离线购物车模式使用示例
class CartSemiOfflineExample {
  static final CartSemiOfflineService _cartService = CartSemiOfflineService();

  /// 示例1：登录后初始化半离线模式
  static Future<void> exampleInitializeAfterLogin() async {
    print('=== 示例1：登录后初始化半离线模式 ===');
    
    // 模拟登录后获取的数据
    final goodsList = [
      GoodsInfo(
        id: 1,
        name: '可乐',
        image: 'cola.jpg',
        price: 5.0,
        stock: 100,
        status: 'A',
      ),
      GoodsInfo(
        id: 2,
        name: '薯条',
        image: 'fries.jpg',
        price: 8.0,
        stock: 50,
        status: 'A',
      ),
    ];

    final memberInfo = MemberInfo(
      id: 123,
      name: '张三',
      mobile: '13800138000',
      level: 'VIP',
    );

    final storeInfo = StoreInfo(
      id: 1,
      name: '测试店铺',
      address: '北京市朝阳区',
    );

    // 初始化半离线模式
    await _cartService.initializeAfterLogin(
      goodsList: goodsList,
      memberInfo: memberInfo,
      storeInfo: storeInfo,
    );

    print('✅ 半离线模式初始化完成');
    print('当前模式: ${_cartService.currentMode}');
  }

  /// 示例2：半离线模式添加商品
  static Future<void> exampleAddToCart() async {
    print('\n=== 示例2：半离线模式添加商品 ===');
    
    // 添加商品到购物车（本地操作）
    final param = CartSaveParam(
      goodsId: 1,
      buyNum: 2,
      specId: null,
      specName: null,
    );

    final success = await _cartService.addToCart(param);
    if (success) {
      print('✅ 商品添加成功');
      
      // 获取购物车统计
      final stats = _cartService.getCartStatistics();
      print('购物车统计: ${stats.totalItems}种商品, ${stats.totalQuantity}件, 总金额¥${stats.totalAmount}');
    } else {
      print('❌ 商品添加失败');
    }
  }

  /// 示例3：半离线模式更新商品数量
  static Future<void> exampleUpdateQuantity() async {
    print('\n=== 示例3：半离线模式更新商品数量 ===');
    
    // 获取购物车列表
    final cartResponse = await _cartService.getCartList();
    if (cartResponse.data.list.isNotEmpty) {
      final firstItem = cartResponse.data.list.first;
      print('当前商品: ${firstItem.goodsName}, 数量: ${firstItem.quantity}');
      
      // 更新数量
      final success = await _cartService.updateCartItem(firstItem.id, 5);
      if (success) {
        print('✅ 数量更新成功');
        
        // 重新获取统计
        final stats = _cartService.getCartStatistics();
        print('更新后统计: ${stats.totalItems}种商品, ${stats.totalQuantity}件, 总金额¥${stats.totalAmount}');
      } else {
        print('❌ 数量更新失败');
      }
    }
  }

  /// 示例4：半离线模式删除商品
  static Future<void> exampleRemoveItem() async {
    print('\n=== 示例4：半离线模式删除商品 ===');
    
    // 获取购物车列表
    final cartResponse = await _cartService.getCartList();
    if (cartResponse.data.list.isNotEmpty) {
      final firstItem = cartResponse.data.list.first;
      print('准备删除商品: ${firstItem.goodsName}');
      
      // 删除商品
      final success = await _cartService.removeCartItem(firstItem.id);
      if (success) {
        print('✅ 商品删除成功');
        
        // 重新获取统计
        final stats = _cartService.getCartStatistics();
        print('删除后统计: ${stats.totalItems}种商品, ${stats.totalQuantity}件, 总金额¥${stats.totalAmount}');
      } else {
        print('❌ 商品删除失败');
      }
    }
  }

  /// 示例5：半离线模式清空购物车
  static Future<void> exampleClearCart() async {
    print('\n=== 示例5：半离线模式清空购物车 ===');
    
    // 先添加一些商品
    await _cartService.addToCart(CartSaveParam(goodsId: 1, buyNum: 3));
    await _cartService.addToCart(CartSaveParam(goodsId: 2, buyNum: 2));
    
    print('添加商品后统计: ${_cartService.getCartStatistics().totalItems}种商品');
    
    // 清空购物车
    final success = await _cartService.clearCart();
    if (success) {
      print('✅ 购物车清空成功');
      
      // 重新获取统计
      final stats = _cartService.getCartStatistics();
      print('清空后统计: ${stats.totalItems}种商品, ${stats.totalQuantity}件, 总金额¥${stats.totalAmount}');
    } else {
      print('❌ 购物车清空失败');
    }
  }

  /// 示例6：半离线模式结算准备
  static Future<void> examplePrepareCheckout() async {
    print('\n=== 示例6：半离线模式结算准备 ===');
    
    // 先添加一些商品
    await _cartService.addToCart(CartSaveParam(goodsId: 1, buyNum: 2));
    await _cartService.addToCart(CartSaveParam(goodsId: 2, buyNum: 1));
    
    try {
      // 准备结算数据（转换为API格式）
      final checkoutData = await _cartService.prepareCheckoutData();
      
      print('✅ 结算数据准备完成');
      print('用户ID: ${checkoutData['userId']}');
      print('商品列表: ${checkoutData['goodsList']}');
      print('总金额: ¥${checkoutData['totalAmount']}');
      
      // 这里可以调用结算API
      // final orderResult = await ApiService.createOrder(checkoutData);
      
      // 模拟结算成功
      await _cartService.onCheckoutSuccess();
      print('✅ 结算成功，本地购物车已清空');
      
    } catch (e) {
      print('❌ 结算准备失败: $e');
    }
  }

  /// 示例7：模式切换
  static Future<void> exampleModeSwitch() async {
    print('\n=== 示例7：模式切换 ===');
    
    // 当前模式
    print('当前模式: ${_cartService.currentMode}');
    
    // 切换到线上模式
    _cartService.setOperationMode(CartOperationMode.online);
    print('切换到线上模式: ${_cartService.currentMode}');
    
    // 切换到离线模式
    _cartService.setOperationMode(CartOperationMode.offline);
    print('切换到离线模式: ${_cartService.currentMode}');
    
    // 切换回半离线模式
    _cartService.setOperationMode(CartOperationMode.semiOffline);
    print('切换回半离线模式: ${_cartService.currentMode}');
  }

  /// 示例8：商品检查功能
  static Future<void> exampleGoodsCheck() async {
    print('\n=== 示例8：商品检查功能 ===');
    
    // 添加商品
    await _cartService.addToCart(CartSaveParam(goodsId: 1, buyNum: 1));
    
    // 检查商品是否在购物车中
    final isInCart = _cartService.isInCart(1);
    print('商品1是否在购物车中: $isInCart');
    
    // 获取购物车中商品数量
    final quantity = _cartService.getCartItemQuantity(1);
    print('商品1在购物车中的数量: $quantity');
    
    // 检查不存在的商品
    final isNotInCart = _cartService.isInCart(999);
    print('商品999是否在购物车中: $isNotInCart');
  }

  /// 运行所有示例
  static Future<void> runAllExamples() async {
    print('🚀 开始运行半离线购物车模式示例\n');
    
    await exampleInitializeAfterLogin();
    await exampleAddToCart();
    await exampleUpdateQuantity();
    await exampleRemoveItem();
    await exampleClearCart();
    await examplePrepareCheckout();
    await exampleModeSwitch();
    await exampleGoodsCheck();
    
    print('\n🎉 所有示例运行完成！');
  }

  /// 半离线模式的优势说明
  static void showAdvantages() {
    print('\n📋 半离线模式优势说明:');
    print('1. 🚀 响应速度快 - 本地操作，无需网络请求');
    print('2. 📱 离线可用 - 网络异常时仍可正常使用');
    print('3. 💾 数据完整 - 登录时获取完整商品信息');
    print('4. 🔄 模式灵活 - 支持多种操作模式切换');
    print('5. 💰 结算准确 - 结算时使用线上API保证数据一致性');
    print('6. 🛡️ 库存安全 - 本地库存检查，避免超卖');
    print('7. 📊 实时统计 - 本地计算，实时更新购物车统计');
    print('8. 🔧 易于维护 - 逻辑清晰，便于调试和维护');
  }

  /// 使用建议
  static void showRecommendations() {
    print('\n💡 使用建议:');
    print('1. 登录成功后立即初始化半离线模式');
    print('2. 定期检查商品库存状态');
    print('3. 结算前验证网络连接');
    print('4. 根据业务需求选择合适的操作模式');
    print('5. 添加适当的错误处理和用户提示');
    print('6. 考虑多设备数据同步需求');
  }
}


