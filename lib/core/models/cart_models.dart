// 购物车操作模式枚举
enum CartOperationMode {
  online,      // 线上模式：所有操作都通过API
  offline,     // 离线模式：完全本地操作，无网络依赖
  semiOffline, // 半离线模式：登录后本地操作，结算时线上处理
  hybrid       // 混合模式：本地优先，后台同步
}

// 同步状态枚举
enum SyncStatus {
  synced,      // 已同步
  pending,     // 待同步
  failed,      // 同步失败
  notRequired  // 无需同步
}

// 本地操作类型枚举
enum LocalOperation {
  add,         // 添加商品
  update,      // 更新数量
  remove,      // 删除商品
  clear        // 清空购物车
}

/// 购物车商品项模型
class CartItem {
  /// 购物车项ID
  final int id;
  
  /// 商品ID
  final int goodsId;
  
  /// 商品名称
  final String goodsName;
  
  /// 商品图片
  final String? goodsImage;
  
  /// 商品价格
  final double price;
  
  /// 购买数量
  final int quantity;
  
  /// 总价格
  final double totalPrice;
  
  /// 规格ID
  final int? specId;
  
  /// 规格名称
  final String? specName;
  
  /// 添加时间
  final DateTime addTime;
  
  /// 更新时间
  final DateTime updateTime;
  
  /// 同步状态
  final SyncStatus syncStatus;

  const CartItem({
    required this.id,
    required this.goodsId,
    required this.goodsName,
    this.goodsImage,
    required this.price,
    required this.quantity,
    required this.totalPrice,
    this.specId,
    this.specName,
    required this.addTime,
    required this.updateTime,
    required this.syncStatus,
  });

  /// 创建空购物车项
  factory CartItem.empty() {
    return CartItem(
      id: 0,
      goodsId: 0,
      goodsName: '',
      price: 0.0,
      quantity: 0,
      totalPrice: 0.0,
      addTime: DateTime.now(),
      updateTime: DateTime.now(),
      syncStatus: SyncStatus.notRequired,
    );
  }

  /// 复制并修改
  CartItem copyWith({
    int? id,
    int? goodsId,
    String? goodsName,
    String? goodsImage,
    double? price,
    int? quantity,
    double? totalPrice,
    int? specId,
    String? specName,
    DateTime? addTime,
    DateTime? updateTime,
    SyncStatus? syncStatus,
  }) {
    return CartItem(
      id: id ?? this.id,
      goodsId: goodsId ?? this.goodsId,
      goodsName: goodsName ?? this.goodsName,
      goodsImage: goodsImage ?? this.goodsImage,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
      totalPrice: totalPrice ?? this.totalPrice,
      specId: specId ?? this.specId,
      specName: specName ?? this.specName,
      addTime: addTime ?? this.addTime,
      updateTime: updateTime ?? this.updateTime,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  factory CartItem.fromJson(Map<String, dynamic> json) {
    return CartItem(
      id: json['id'] ?? 0,
      goodsId: json['goodsId'] ?? 0,
      goodsName: json['goodsName'] ?? '',
      goodsImage: json['goodsImage'],
      price: (json['price'] ?? 0).toDouble(),
      quantity: json['quantity'] ?? 0,
      totalPrice: (json['totalPrice'] ?? 0).toDouble(),
      specId: json['specId'],
      specName: json['specName'],
      addTime: DateTime.tryParse(json['addTime'] ?? '') ?? DateTime.now(),
      updateTime: DateTime.tryParse(json['updateTime'] ?? '') ?? DateTime.now(),
      syncStatus: SyncStatus.values.firstWhere(
        (e) => e.toString() == 'SyncStatus.${json['syncStatus']}',
        orElse: () => SyncStatus.notRequired,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'goodsId': goodsId,
      'goodsName': goodsName,
      'goodsImage': goodsImage,
      'price': price,
      'quantity': quantity,
      'totalPrice': totalPrice,
      'specId': specId,
      'specName': specName,
      'addTime': addTime.toIso8601String(),
      'updateTime': updateTime.toIso8601String(),
      'syncStatus': syncStatus.toString().split('.').last,
    };
  }
}

/// 商品信息模型（简化版，用于购物车）
class GoodsInfo {
  /// 商品ID
  final int id;
  
  /// 商品名称
  final String name;
  
  /// 商品图片
  final String? image;
  
  /// 商品价格
  final double price;
  
  /// 商品库存
  final int stock;
  
  /// 商品状态
  final String? status;

  const GoodsInfo({
    required this.id,
    required this.name,
    this.image,
    required this.price,
    required this.stock,
    this.status,
  });

  factory GoodsInfo.fromJson(Map<String, dynamic> json) {
    return GoodsInfo(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      image: json['image'],
      price: (json['price'] ?? 0).toDouble(),
      stock: json['stock'] ?? 0,
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'price': price,
      'stock': stock,
      'status': status,
    };
  }
}

/// 会员信息模型（简化版）
class MemberInfo {
  /// 会员ID
  final int id;
  
  /// 会员名称
  final String? name;
  
  /// 会员手机号
  final String? mobile;
  
  /// 会员等级
  final String? level;

  const MemberInfo({
    required this.id,
    this.name,
    this.mobile,
    this.level,
  });

  factory MemberInfo.fromJson(Map<String, dynamic> json) {
    return MemberInfo(
      id: json['id'] ?? 0,
      name: json['name'],
      mobile: json['mobile'],
      level: json['level'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'mobile': mobile,
      'level': level,
    };
  }
}

/// 店铺信息模型（简化版）
class StoreInfo {
  /// 店铺ID
  final int id;
  
  /// 店铺名称
  final String name;
  
  /// 店铺地址
  final String? address;

  const StoreInfo({
    required this.id,
    required this.name,
    this.address,
  });

  factory StoreInfo.fromJson(Map<String, dynamic> json) {
    return StoreInfo(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      address: json['address'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
    };
  }
}

/// 购物车保存参数
class CartSaveParam {
  /// 商品ID
  final int goodsId;
  
  /// 购买数量
  final int buyNum;
  
  /// 规格ID
  final int? specId;
  
  /// 规格名称
  final String? specName;
  
  /// 用户ID
  final int? userId;
  
  /// 桌码ID
  final int? tableId;
  
  /// 挂单号
  final String? hangNo;

  const CartSaveParam({
    required this.goodsId,
    required this.buyNum,
    this.specId,
    this.specName,
    this.userId,
    this.tableId,
    this.hangNo,
  });

  factory CartSaveParam.fromJson(Map<String, dynamic> json) {
    return CartSaveParam(
      goodsId: json['goodsId'] ?? 0,
      buyNum: json['buyNum'] ?? 0,
      specId: json['specId'],
      specName: json['specName'],
      userId: json['userId'],
      tableId: json['tableId'],
      hangNo: json['hangNo'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'goodsId': goodsId,
      'buyNum': buyNum,
      'specId': specId,
      'specName': specName,
      'userId': userId,
      'tableId': tableId,
      'hangNo': hangNo,
    };
  }
}

/// 购物车列表响应数据
class CartListResponse {
  /// 响应码
  final int code;
  
  /// 响应消息
  final String message;
  
  /// 响应数据
  final CartListData data;

  const CartListResponse({
    required this.code,
    required this.message,
    required this.data,
  });

  factory CartListResponse.fromJson(Map<String, dynamic> json) {
    return CartListResponse(
      code: json['code'] ?? 0,
      message: json['message'] ?? '',
      data: CartListData.fromJson(json['data'] ?? {}),
    );
  }
}

/// 购物车列表数据
class CartListData {
  /// 购物车商品列表
  final List<CartItem> list;
  
  /// 总金额
  final double totalAmount;
  
  /// 总数量
  final int totalCount;
  
  /// 优惠券列表
  final List<CartCoupon> coupons;

  const CartListData({
    required this.list,
    required this.totalAmount,
    required this.totalCount,
    required this.coupons,
  });

  factory CartListData.fromJson(Map<String, dynamic> json) {
    return CartListData(
      list: (json['list'] as List?)
          ?.map((item) => CartItem.fromJson(item))
          .toList() ?? [],
      totalAmount: (json['totalAmount'] ?? 0).toDouble(),
      totalCount: json['totalCount'] ?? 0,
      coupons: (json['coupons'] as List?)
          ?.map((item) => CartCoupon.fromJson(item))
          .toList() ?? [],
    );
  }
}

/// 购物车优惠券
class CartCoupon {
  /// 优惠券ID
  final int id;
  
  /// 优惠券名称
  final String name;
  
  /// 优惠券类型
  final String type;
  
  /// 优惠金额
  final double amount;

  const CartCoupon({
    required this.id,
    required this.name,
    required this.type,
    required this.amount,
  });

  factory CartCoupon.fromJson(Map<String, dynamic> json) {
    return CartCoupon(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
    );
  }
}

/// 购物车统计信息
class CartStatistics {
  /// 商品种类数
  final int totalItems;
  
  /// 商品总数量
  final int totalQuantity;
  
  /// 总金额
  final double totalAmount;

  const CartStatistics({
    required this.totalItems,
    required this.totalQuantity,
    required this.totalAmount,
  });
}

/// 购物车操作记录
class CartOperation {
  /// 操作ID
  final int id;
  
  /// 操作类型
  final LocalOperation operation;
  
  /// 操作参数
  final Map<String, dynamic> params;
  
  /// 操作时间
  final DateTime operationTime;
  
  /// 同步状态
  final SyncStatus syncStatus;

  const CartOperation({
    required this.id,
    required this.operation,
    required this.params,
    required this.operationTime,
    required this.syncStatus,
  });

  /// 复制并修改
  CartOperation copyWith({
    int? id,
    LocalOperation? operation,
    Map<String, dynamic>? params,
    DateTime? operationTime,
    SyncStatus? syncStatus,
  }) {
    return CartOperation(
      id: id ?? this.id,
      operation: operation ?? this.operation,
      params: params ?? this.params,
      operationTime: operationTime ?? this.operationTime,
      syncStatus: syncStatus ?? this.syncStatus,
    );
  }

  factory CartOperation.fromJson(Map<String, dynamic> json) {
    return CartOperation(
      id: json['id'] ?? 0,
      operation: LocalOperation.values.firstWhere(
        (e) => e.toString() == 'LocalOperation.${json['operation']}',
        orElse: () => LocalOperation.add,
      ),
      params: Map<String, dynamic>.from(json['params'] ?? {}),
      operationTime: DateTime.tryParse(json['operationTime'] ?? '') ?? DateTime.now(),
      syncStatus: SyncStatus.values.firstWhere(
        (e) => e.toString() == 'SyncStatus.${json['syncStatus']}',
        orElse: () => SyncStatus.pending,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'operation': operation.toString().split('.').last,
      'params': params,
      'operationTime': operationTime.toIso8601String(),
      'syncStatus': syncStatus.toString().split('.').last,
    };
  }
}
