# \u57FA\u672C\u914D\u7F6E
server.port=8080
env.profile=dev
env.properties.path=C:/Code/fuintCatering/fuintBackend/configure/

# \u6570\u636E\u5E93\u914D\u7F6E
spring.datasource.type=com.zaxxer.hikari.HikariDataSource
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.cache.type=ehcache
spring.cache.ehcache.config=classpath:ehcache.xml

# \u65E5\u5FD7\u7EA7\u522B
logging.level.com.fuint=info
multipart.max-file-size=20mb
multipart.max-request-size=20mb

# \u6700\u5927\u4E0A\u4F20\u6587\u4EF6
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# mybatis\u914D\u7F6E
mybatis-plus.mapper-locations = classpath*:/mapper/*.xml

# \u9ED8\u8BA4\u65F6\u95F4\u683C\u5F0F
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss

mybatis.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
