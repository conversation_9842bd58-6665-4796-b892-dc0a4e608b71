package com.fuint.module.clientApi.controller;

import com.fuint.common.dto.UserInfo;
import com.fuint.common.service.SettingService;
import com.fuint.common.util.TokenUtil;
import com.fuint.framework.web.BaseController;
import com.fuint.framework.web.ResponseObject;
import com.fuint.repository.model.MtSetting;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 商品相关API控制器
 *
 * Created by FSQ
 * CopyRight https://www.fuint.cn
 */
@Api(tags="会员端-商品相关接口")
@RestController
@RequestMapping(value = "/clientApi/goodsApi")
public class ClientGoodsApiController extends BaseController {

    @Autowired
    private SettingService settingService;

    /**
     * 获取商品备注列表
     *
     * @param request HttpServletRequest
     * @return 备注列表
     */
    @ApiOperation(value = "获取商品备注列表")
    @RequestMapping(value = "/remarkList", method = RequestMethod.GET)
    @CrossOrigin
    public ResponseObject getRemarkList(HttpServletRequest request) {
        String token = request.getHeader("Access-Token");
        UserInfo userInfo = TokenUtil.getUserInfoByToken(token);

        if (null == userInfo) {
            return getFailureResult(1001, "请先登录");
        }

        try {
            // 获取商户ID
            Integer merchantId = userInfo.getMerchantId();
            
            // 从系统设置中获取备注列表配置
            List<String> remarkList = getRemarkListFromSettings(merchantId);
            
            // 如果没有配置，返回默认备注列表
            if (remarkList.isEmpty()) {
                remarkList = getDefaultRemarkList();
            }

            return getSuccessResult(remarkList);
        } catch (Exception e) {
            return getFailureResult(201, "获取备注列表失败: " + e.getMessage());
        }
    }

    /**
     * 从系统设置中获取备注列表
     *
     * @param merchantId 商户ID
     * @return 备注列表
     */
    private List<String> getRemarkListFromSettings(Integer merchantId) {
        try {
            // 查找备注列表配置
            List<MtSetting> settingList = settingService.getSettingList(merchantId, "GOODS_REMARK");
            
            for (MtSetting setting : settingList) {
                if ("remarkList".equals(setting.getName()) && setting.getValue() != null) {
                    // 解析备注列表，支持逗号分隔或换行分隔
                    String value = setting.getValue().trim();
                    if (!value.isEmpty()) {
                        // 先按逗号分隔，再按换行分隔
                        String[] remarks;
                        if (value.contains(",")) {
                            remarks = value.split(",");
                        } else {
                            remarks = value.split("\\n");
                        }
                        
                        List<String> result = new ArrayList<>();
                        for (String remark : remarks) {
                            String trimmed = remark.trim();
                            if (!trimmed.isEmpty()) {
                                result.add(trimmed);
                            }
                        }
                        return result;
                    }
                }
            }
        } catch (Exception e) {
            // 记录错误但不抛出异常，使用默认列表
            logger.error("获取备注列表配置失败: " + e.getMessage(), e);
        }
        
        return new ArrayList<>();
    }

    /**
     * 获取默认备注列表
     *
     * @return 默认备注列表
     */
    private List<String> getDefaultRemarkList() {
        return Arrays.asList(
            "少盐",
            "少糖",
            "多糖",
            "加冰",
            "少冰",
            "不要葱",
            "不要香菜",
            "不要辣",
            "微辣",
            "中辣",
            "特辣",
            "不要蒜",
            "多放醋",
            "少放油"
        );
    }

    /**
     * 更新商品备注列表配置
     *
     * @param request HttpServletRequest
     * @param remarkList 备注列表（逗号分隔的字符串）
     * @return 更新结果
     */
    @ApiOperation(value = "更新商品备注列表配置")
    @RequestMapping(value = "/remarkList", method = RequestMethod.POST)
    @CrossOrigin
    public ResponseObject updateRemarkList(HttpServletRequest request, @RequestParam String remarkList) {
        String token = request.getHeader("Access-Token");
        UserInfo userInfo = TokenUtil.getUserInfoByToken(token);

        if (null == userInfo) {
            return getFailureResult(1001, "请先登录");
        }

        try {
            // 获取商户ID
            Integer merchantId = userInfo.getMerchantId();
            
            // 创建或更新设置
            MtSetting setting = new MtSetting();
            setting.setMerchantId(merchantId);
            setting.setType("GOODS_REMARK");
            setting.setName("remarkList");
            setting.setValue(remarkList);
            setting.setDescription("商品备注列表配置");
            
            settingService.saveSetting(setting);
            
            return getSuccessResult("备注列表更新成功");
        } catch (Exception e) {
            return getFailureResult(201, "更新备注列表失败: " + e.getMessage());
        }
    }
}
