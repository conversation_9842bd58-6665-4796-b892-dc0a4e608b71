ALTER TABLE `mt_table`
  ADD COLUMN `AREA_ID` INT DEFAULT 0 NULL COMMENT '区域ID' AFTER `CODE`;

CREATE TABLE `mt_table_area` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `NAME` varchar(50) NOT NULL DEFAULT '' COMMENT '区域名称',
  `MERCHANT_ID` int NOT NULL DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int NOT NULL DEFAULT '0' COMMENT '所属店铺ID',
  `DESCRIPTION` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注信息',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT NULL COMMENT '最后操作人',
  `SORT` int DEFAULT '0' COMMENT '排序',
  `STATUS` char(1) DEFAULT 'A' COMMENT 'A：正常；D：删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='桌码区域表';
