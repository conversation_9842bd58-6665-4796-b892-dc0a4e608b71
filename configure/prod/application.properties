# 数据库配置
spring.datasource.url=******************************************************************************************
spring.datasource.username=root
spring.datasource.password=root

# Redis配置
spring.session.store-type=redis
spring.session.redis.namespace=fuint
# Redis数据库索引（默认为0）
spring.redis.database=0 
# Redis服务器地址(生产)
spring.redis.host=127.0.0.1
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.pool.max-active=-1
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.pool.max-wait=-1 
# 连接池中的最大空闲连接
spring.redis.pool.max-idle=8 
# 连接池中的最小空闲连接
spring.redis.pool.min-idle=0 
# 连接超时时间（毫秒）
spring.redis.timeout=0

# 系统名称
system.name = fuint餐饮会员系统

# 前端h5地址
website.url=https://www.fuint.cn/h5/

# 上传图片本地地址
images.root=/www/wwwroot/www.xxx.com
images.path=/static/uploadImages/

# 上传图片服务器域名
images.upload.url=http://localhost:8080

# 上传图片允许的大小（单位：MB）
images.upload.maxSize=5

################## 定时脚本配置 #########################
# 定时发送消息
message.job.switch = 1
message.job.time = 0 0/1 * * * ?

# 卡券到期处理
couponExpire.job.switch = 1
couponExpire.job.time = 0 0/1 * * * ?

# 订单超时取消
orderCancel.job.switch = 1
orderCancel.job.time = 0 0/1 * * * ?

# 分佣提成计算
commission.job.switch = 1
commission.job.time = 0 0/1 * * * ?

################## 阿里云短信配置 #######################
# 短信接口模式[0-关闭 1-打开]
aliyun.sms.mode = 0
aliyun.sms.accessKeyId=LTAI4GJMjV123oXsrQJLnPZt
aliyun.sms.accessKeySecret=eGVBL30u5Ypj234d7XODlwYKWTaGT
# 阿里云短信签名
aliyun.sms.signName=延禾技术

################## 阿里云OSS存储配置######################
# 模式[0-关闭 1-打开]
aliyun.oss.mode = 0
aliyun.oss.accessKeyId = LTAI4GJMjVhBa212rQJLnPZt
aliyun.oss.accessKeySecret = eGVBL30u53456gXd7XODlwYKWTaGT
aliyun.oss.endpoint = https://oss-cn-shenzhen.aliyuncs.com
aliyun.oss.bucketName = fuint-application
# 上传文件夹
aliyun.oss.folder = uploads
# 访问域名
aliyun.oss.domain = https://fuint-application.oss-cn-shenzhen.aliyuncs.com

################## 微信相关配置 ##########################
# 公众号配置
weixin.official.appId=wxf4327ef05c27a0
weixin.official.appSecret=******************************

# 小程序配置
wxpay.appId = wxb6af3741234162bc
wxpay.appSecret = 76a538bfa5b55a4564d5f2be5540
wxpay.mchId=1636980812
wxpay.apiV2=34354320201030y323e432342343
wxpay.certPath=/usr/local/fuint/cert/apiclient_cert.p12
wxpay.domain=https://www.fuint.cn/fuint-application

################## 支付宝支付相关配置 ######################
alipay.appId = 应用编号
alipay.privateKey = 应用私钥
alipay.publicKey = 支付宝公钥（通过应用公钥上传到支付宝开放平台换取支付宝公钥）
alipay.serverUrl=https://openapi.alipay.com/gateway.do
alipay.domain=https://www.fuint.cn/fuint-application/clientApi/pay/aliPayCallback

################ 微信订阅模板消息配置 ######################
weixin.subMessage.orderCreated=[{'key':'time', 'name':'订单时间'},{'key':'orderSn', 'name':'订单号'},{'key':'remark', 'name':'备注信息'}]
weixin.subMessage.deliverGoods=[{'key':'receiver', 'name':'收货人'}, {'key':'orderSn', 'name':'订单号'}, {'key':'expressCompany', 'name':'快递公司'}, {'key':'expressNo', 'name':'快递单号'}]
weixin.subMessage.couponExpire=[{'key':'name', 'name':'卡券名称'}, {'key':'expireTime', 'name':'到期时间'},{'key':'tips', 'name':'温馨提示'}]
weixin.subMessage.couponArrival=[{'key':'name', 'name':'卡券名称'},{'key':'amount', 'name':'金额'},{'key':'tips', 'name':'温馨提示'}]
weixin.subMessage.balanceChange=[{'key':'amount', 'name':'变动金额'},{'key':'time', 'name':'变动时间'},{'key':'tips', 'name':'温馨提示'}]
weixin.subMessage.couponConfirm=[{'key':'name', 'name':'卡券名称'},{'key':'time', 'name':'核销时间'}]
weixin.subMessage.pointChange=[{'key':'amount', 'name':'变动数量'},{'key':'time', 'name':'变动时间'},{'key':'remark', 'name':'备注信息'}]