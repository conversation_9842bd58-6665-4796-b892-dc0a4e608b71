# Flutter 收银台应用 - 数据模型文档

## 概述

本文档描述了Flutter收银台应用中使用的数据模型，包括API响应模型、购物车模型以及各种操作模式。

## 模型文件结构

```
lib/core/models/
├── cashier_init_response.dart          # 收银台初始化响应模型
├── cashier_init_response_example.dart  # 收银台初始化使用示例
├── user_info_response.dart             # 用户信息响应模型
├── user_info_response_example.dart     # 用户信息使用示例
├── cart_models.dart                    # 购物车相关模型
├── cart_models_example.dart            # 购物车使用示例
├── cart_semi_offline_example.dart      # 半离线购物车模式示例
└── analytics_models.dart               # 分析数据模型（参考实现）
```

## 购物车操作模式

### 模式枚举

```dart
enum CartOperationMode {
  online,      // 线上模式：所有操作都通过API
  offline,     // 离线模式：完全本地操作，无网络依赖
  semiOffline, // 半离线模式：登录后本地操作，结算时线上处理
  hybrid       // 混合模式：本地优先，后台同步
}
```

### 半离线模式详解

#### 什么是半离线模式？

半离线模式是一种创新的购物车操作策略，结合了本地操作的快速响应和线上结算的数据一致性优势。

**核心特点：**
- **登录时获取完整数据**：用户登录成功后，一次性获取商品列表、分类、规格等完整信息
- **本地购物车操作**：所有购物车操作（添加、删除、修改）都在本地进行，无需网络请求
- **结算时线上处理**：最终结算时使用线上API，确保数据一致性和业务逻辑正确性

#### 半离线模式的优势

| 优势 | 说明 |
|------|------|
| 🚀 **响应速度快** | 本地操作，无需网络请求，用户体验流畅 |
| 📱 **离线可用** | 网络异常时仍可正常使用购物车功能 |
| 💾 **数据完整** | 登录时获取完整商品信息，支持本地库存检查 |
| 🔄 **模式灵活** | 支持多种操作模式切换，适应不同场景 |
| 💰 **结算准确** | 结算时使用线上API，保证数据一致性 |
| 🛡️ **库存安全** | 本地库存检查，避免超卖问题 |
| 📊 **实时统计** | 本地计算，实时更新购物车统计信息 |
| 🔧 **易于维护** | 逻辑清晰，便于调试和维护 |

#### 半离线模式的工作流程

```mermaid
graph TD
    A[用户登录] --> B[获取商品列表/分类/规格]
    B --> C[初始化半离线模式]
    C --> D[本地购物车操作]
    D --> E[添加商品]
    D --> F[删除商品]
    D --> G[修改数量]
    D --> H[清空购物车]
    E --> I[本地存储]
    F --> I
    G --> I
    H --> I
    I --> J[用户结算]
    J --> K[转换为API格式]
    K --> L[调用线上结算API]
    L --> M[清空本地购物车]
```

#### 使用示例

```dart
// 1. 登录后初始化
await cartService.initializeAfterLogin(
  goodsList: goodsList,      // 商品列表
  memberInfo: memberInfo,    // 会员信息
  storeInfo: storeInfo,      // 店铺信息
);

// 2. 添加商品（本地操作）
final success = await cartService.addToCart(CartSaveParam(
  goodsId: 1,
  buyNum: 2,
));

// 3. 获取购物车（本地数据）
final cartResponse = await cartService.getCartList();

// 4. 结算准备（转换为API格式）
final checkoutData = await cartService.prepareCheckoutData();

// 5. 调用结算API
final orderResult = await ApiService.createOrder(checkoutData);

// 6. 结算成功后清空本地购物车
await cartService.onCheckoutSuccess();
```

## API响应模型

### 1. 收银台初始化响应 (CashierInitResponse)

**接口路径：** `/cashier/init/{userId}`

**响应结构：**
```dart
class CashierInitResponse {
  final String imagePath;                    // 图片基础路径
  final StoreInfo? storeInfo;                // 店铺信息
  final MemberInfo? memberInfo;              // 会员信息
  final AccountInfo accountInfo;             // 账户信息
  final List<GoodsInfo> goodsList;           // 商品列表
  final int totalGoods;                      // 商品总数
  final List<GoodsCategory> cateList;        // 分类列表
}
```

**使用示例：**
```dart
// 解析API响应
final response = CashierInitResponse.fromJson(jsonData);

// 获取商品列表
final goodsList = response.goodsList;

// 获取店铺信息
final storeInfo = response.storeInfo;

// 获取会员信息
final memberInfo = response.memberInfo;
```

### 2. 用户信息响应 (UserInfoResponse)

**接口路径：** `/backendApi/login/getInfo`

**响应结构：**
```dart
class UserInfoResponse {
  final AccountInfo accountInfo;             // 账户信息
  final List<RoleInfo> roles;                // 角色列表
  final List<MenuInfo> permissions;          // 权限列表
}
```

**使用示例：**
```dart
// 解析API响应
final response = UserInfoResponse.fromJson(jsonData);

// 检查用户权限
final hasPermission = response.permissions.any(
  (menu) => menu.path == '/cashier'
);

// 获取用户角色
final userRoles = response.roles.map((role) => role.name).toList();
```

## 购物车模型

### 核心模型类

#### CartItem - 购物车商品项
```dart
class CartItem {
  final int id;                    // 购物车项ID
  final int goodsId;               // 商品ID
  final String goodsName;          // 商品名称
  final String? goodsImage;        // 商品图片
  final double price;              // 商品价格
  final int quantity;              // 购买数量
  final double totalPrice;         // 总价格
  final int? specId;               // 规格ID
  final String? specName;          // 规格名称
  final DateTime addTime;          // 添加时间
  final DateTime updateTime;       // 更新时间
  final SyncStatus syncStatus;     // 同步状态
}
```

#### CartSaveParam - 购物车保存参数
```dart
class CartSaveParam {
  final int goodsId;               // 商品ID
  final int buyNum;                // 购买数量
  final int? specId;               // 规格ID
  final String? specName;          // 规格名称
  final int? userId;               // 用户ID
  final int? tableId;              // 桌码ID
  final String? hangNo;            // 挂单号
}
```

#### CartListResponse - 购物车列表响应
```dart
class CartListResponse {
  final int code;                  // 响应码
  final String message;            // 响应消息
  final CartListData data;         // 响应数据
}

class CartListData {
  final List<CartItem> list;       // 购物车商品列表
  final double totalAmount;        // 总金额
  final int totalCount;            // 总数量
  final List<CartCoupon> coupons;  // 优惠券列表
}
```

### 同步状态枚举

```dart
enum SyncStatus {
  synced,      // 已同步
  pending,     // 待同步
  failed,      // 同步失败
  notRequired  // 无需同步
}
```

## 服务类

### CartSemiOfflineService - 半离线购物车服务

**主要方法：**

| 方法 | 说明 | 参数 | 返回值 |
|------|------|------|--------|
| `initialize()` | 初始化服务 | - | `Future<void>` |
| `initializeAfterLogin()` | 登录后初始化 | `goodsList`, `memberInfo`, `storeInfo` | `Future<void>` |
| `addToCart()` | 添加商品 | `CartSaveParam` | `Future<bool>` |
| `updateCartItem()` | 更新商品数量 | `itemId`, `newQuantity` | `Future<bool>` |
| `removeCartItem()` | 删除商品 | `itemId` | `Future<bool>` |
| `clearCart()` | 清空购物车 | - | `Future<bool>` |
| `getCartList()` | 获取购物车列表 | - | `Future<CartListResponse>` |
| `prepareCheckoutData()` | 准备结算数据 | - | `Future<Map<String, dynamic>>` |
| `onCheckoutSuccess()` | 结算成功处理 | - | `Future<void>` |
| `getCartStatistics()` | 获取统计信息 | - | `CartStatistics` |
| `isInCart()` | 检查商品是否在购物车 | `goodsId`, `specId?` | `bool` |
| `getCartItemQuantity()` | 获取商品数量 | `goodsId`, `specId?` | `int` |

**使用示例：**
```dart
// 创建服务实例
final cartService = CartSemiOfflineService();

// 设置操作模式
cartService.setOperationMode(CartOperationMode.semiOffline);

// 添加商品
final success = await cartService.addToCart(CartSaveParam(
  goodsId: 1,
  buyNum: 2,
));

// 获取购物车统计
final stats = cartService.getCartStatistics();
print('总金额: ¥${stats.totalAmount}');
```

## 最佳实践

### 1. 模式选择建议

| 场景 | 推荐模式 | 原因 |
|------|----------|------|
| 网络稳定，实时性要求高 | `online` | 数据实时同步 |
| 网络不稳定，需要离线使用 | `semiOffline` | 平衡性能和可靠性 |
| 完全离线环境 | `offline` | 无网络依赖 |
| 复杂业务场景 | `hybrid` | 灵活性和可靠性 |

### 2. 错误处理

```dart
try {
  final success = await cartService.addToCart(param);
  if (success) {
    // 操作成功
  } else {
    // 操作失败，显示错误信息
  }
} catch (e) {
  // 异常处理
  print('购物车操作异常: $e');
}
```

### 3. 数据验证

```dart
// 添加商品前验证库存
final goods = goodsList.firstWhere((g) => g.id == goodsId);
if (goods.stock < quantity) {
  throw Exception('库存不足');
}

// 验证商品状态
if (goods.status != 'A') {
  throw Exception('商品已下架');
}
```

### 4. 性能优化

- 使用本地存储减少网络请求
- 批量操作减少API调用次数
- 合理设置缓存过期时间
- 异步处理避免阻塞UI

## 注意事项

1. **数据一致性**：半离线模式下，本地数据可能与服务器数据不一致，结算时需要验证
2. **库存管理**：本地库存检查可能不是最新的，关键操作时需要重新验证
3. **网络状态**：需要监控网络状态，在网络恢复时进行数据同步
4. **错误处理**：需要完善的错误处理机制，包括网络异常、数据异常等
5. **用户体验**：提供清晰的状态提示，让用户了解当前操作模式

## 更新日志

- **v1.4.0** - 添加半离线购物车模式
- **v1.3.0** - 添加用户信息响应模型
- **v1.2.0** - 添加购物车相关模型
- **v1.1.0** - 添加收银台初始化响应模型
- **v1.0.0** - 初始版本


